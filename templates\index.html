<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messdienerplan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .header-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .plan-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .table th {
            background-color: #495057;
            color: white;
            border: none;
        }
        .table td {
            border-color: #dee2e6;
        }
        .admin-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="header-bg py-4 mb-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="mb-0"><i class="bi bi-calendar-check"></i> Messdienerplan</h1>
                    <p class="mb-0 opacity-75">Aktuelle Einteilung der Messdiener</p>
                </div>
                {% if session.admin %}
                <div class="col-auto">
                    <a href="{{ url_for('edit') }}" class="btn btn-light me-2">
                        <i class="bi bi-pencil"></i> Bearbeiten
                    </a>
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-light">
                        <i class="bi bi-box-arrow-right"></i> Abmelden
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="plan-table p-4">
            {% if plan and plan|length > 1 %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                {% for header in plan[0] %}
                                    <th>{{ header }}</th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in plan[1:] %}
                                <tr>
                                    <td><strong>{{ row[0] if row[0] else '-' }}</strong></td>
                                    <td>
                                        {% if row|length > 1 and row[1] %}
                                            {% set messdiener_list = row[1].split(',') %}
                                            {% for messdiener in messdiener_list %}
                                                <span class="badge bg-primary me-1 mb-1">{{ messdiener.strip() }}</span>
                                            {% endfor %}
                                        {% else %}
                                            <span class="text-muted">Noch keine Einteilung</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-calendar-x display-1 text-muted"></i>
                    <h3 class="mt-3 text-muted">Noch kein Plan verfügbar</h3>
                    <p class="text-muted">Der Messdienerplan ist noch nicht erstellt worden.</p>
                    {% if session.admin %}
                        <a href="{{ url_for('edit') }}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> Plan erstellen
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <div class="mt-4 text-center text-muted">
            <small>
                <i class="bi bi-info-circle"></i>
                Letzte Aktualisierung: {{ moment().format('DD.MM.YYYY HH:mm') if moment else 'Unbekannt' }}
            </small>
        </div>
    </div>

    {% if not session.admin %}
        <a href="{{ url_for('login') }}" class="btn btn-primary admin-btn rounded-circle" title="Administrator-Anmeldung">
            <i class="bi bi-key"></i>
        </a>
    {% endif %}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>