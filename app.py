from flask import Flask, render_template, request, redirect, url_for, session
import csv

app = Flask(__name__)
app.secret_key = 'geheim'

# CSV e<PERSON>lesen
def load_plan():
    with open('data/plan.csv') as f:
        reader = csv.reader(f)
        return list(reader)

# CSV speichern
def save_plan(plan):
    with open('data/plan.csv', 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerows(plan)

@app.route('/')
def index():
    plan = load_plan()
    return render_template('index.html', plan=plan)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        if request.form['password'] == 'adminpass':
            session['admin'] = True
            return redirect(url_for('edit'))
    return render_template('login.html')

@app.route('/edit', methods=['GET', 'POST'])
def edit():
    if not session.get('admin'):
        return redirect(url_for('login'))
    
    plan = load_plan()
    if request.method == 'POST':
        for i in range(len(plan)):
            plan[i] = [request.form.get(f'date_{i}'), request.form.get(f'name_{i}')]
        save_plan(plan)
        return redirect(url_for('index'))
    return render_template('edit.html', plan=plan)

@app.route('/logout')
def logout():
    session.pop('admin', None)
    return redirect(url_for('index'))

if __name__ == '__main__':
    app.run()
